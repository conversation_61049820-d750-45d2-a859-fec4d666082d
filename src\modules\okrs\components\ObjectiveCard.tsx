import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import {
  Button,
  Chip,
  CollapsibleCard,
  IconCard,
  ProgressBar,
  Typography,
} from '@/shared/components/common';

import { useKeyResultsByObjective, useUpdateKeyResult } from '../hooks/useKeyResults';
import { KeyResultDto, UpdateKeyResultDto } from '../types/key-result.types';
import { ObjectiveDto } from '../types/objective.types';

// Component Modal cập nhật Key Result
interface KeyResultUpdateModalProps {
  keyResult: KeyResultDto;
  isOpen: boolean;
  onClose: () => void;
  onUpdate: (data: UpdateKeyResultDto) => void;
}

const KeyResultUpdateModal: React.FC<KeyResultUpdateModalProps> = ({
  keyResult,
  isOpen,
  onClose,
  onUpdate,
}) => {
  const { t } = useTranslation(['okrs', 'common']);
  const [currentValue, setCurrentValue] = useState(keyResult.currentValue || 0);

  const handleSubmit = () => {
    onUpdate({ currentValue });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
        <Typography variant="h5" className="mb-4">
          {t('okrs:keyResult.update', 'Cập nhật Key Result')}
        </Typography>

        <div className="space-y-4">
          <div>
            <Typography variant="body2" className="font-medium mb-1">
              {keyResult.title}
            </Typography>
            <Typography variant="caption" className="text-gray-500">
              {t('okrs:keyResult.target', 'Mục tiêu')}: {keyResult.targetValue}{' '}
              {keyResult.unit || ''}
            </Typography>
          </div>

          <div>
            <Typography variant="body2" className="font-medium mb-2">
              {t('okrs:keyResult.currentValue', 'Giá trị hiện tại')}
            </Typography>
            <input
              type="number"
              value={currentValue}
              onChange={e => setCurrentValue(parseFloat(e.target.value) || 0)}
              className="w-full p-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              min={0}
              max={keyResult.targetValue}
            />
          </div>

          <div>
            <Typography variant="caption" className="text-gray-500">
              {t('okrs:keyResult.progress', 'Tiến độ')}:{' '}
              {Math.round((currentValue / keyResult.targetValue) * 100)}%
            </Typography>
          </div>
        </div>

        <div className="flex justify-end gap-2 mt-6">
          <Button variant="outline" onClick={onClose}>
            {t('common:cancel', 'Hủy')}
          </Button>
          <Button variant="primary" onClick={handleSubmit}>
            {t('common:save', 'Lưu')}
          </Button>
        </div>
      </div>
    </div>
  );
};

interface ObjectiveCardProps {
  /**
   * Dữ liệu objective
   */
  objective: ObjectiveDto;

  /**
   * Callback khi thêm key result
   */
  onAddKeyResult?: () => void;
}

/**
 * Component hiển thị objective dạng card với key results
 */
const ObjectiveCard: React.FC<ObjectiveCardProps> = ({ objective, onAddKeyResult }) => {
  const { t } = useTranslation(['okrs', 'common']);

  // State cho modal cập nhật key result
  const [editingKeyResult, setEditingKeyResult] = useState<KeyResultDto | null>(null);
  const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false);

  // Hook để cập nhật key result
  const updateKeyResultMutation = useUpdateKeyResult();

  // Lấy key results của objective
  const { data: keyResults, isLoading: isLoadingKeyResults } = useKeyResultsByObjective(
    objective.id
  );

  // Xử lý mở modal cập nhật key result
  const handleEditKeyResult = (keyResult: KeyResultDto) => {
    setEditingKeyResult(keyResult);
    setIsUpdateModalOpen(true);
  };

  // Xử lý đóng modal cập nhật
  const handleCloseUpdateModal = () => {
    setIsUpdateModalOpen(false);
    setEditingKeyResult(null);
  };

  // Xử lý cập nhật key result
  const handleUpdateKeyResult = async (data: UpdateKeyResultDto) => {
    if (!editingKeyResult) return;

    try {
      await updateKeyResultMutation.mutateAsync({
        id: editingKeyResult.id,
        data,
      });
      handleCloseUpdateModal();
    } catch (error) {
      console.error('Error updating key result:', error);
    }
  };

  // Tính toán progress tổng thể
  const overallProgress = React.useMemo(() => {
    if (!keyResults || keyResults.length === 0) return 0;
    const totalProgress = keyResults.reduce((sum, kr) => sum + (kr.progress || 0), 0);
    return Math.round(totalProgress / keyResults.length);
  }, [keyResults]);

  // Render status chip
  const renderStatusChip = (status: string | null) => {
    const statusConfig = {
      NOT_STARTED: { variant: 'secondary', label: t('okrs:status.notStarted', 'Chưa bắt đầu') },
      IN_PROGRESS: { variant: 'primary', label: t('okrs:status.inProgress', 'Đang thực hiện') },
      COMPLETED: { variant: 'success', label: t('okrs:status.completed', 'Hoàn thành') },
      AT_RISK: { variant: 'warning', label: t('okrs:status.atRisk', 'Có rủi ro') },
      CANCELLED: { variant: 'danger', label: t('okrs:status.cancelled', 'Đã hủy') },
    };

    const safeStatus = status || 'NOT_STARTED';
    const config =
      statusConfig[safeStatus as keyof typeof statusConfig] || statusConfig.NOT_STARTED;
    return <Chip variant={config.variant as any}>{config.label}</Chip>;
  };

  // Render type badge
  const renderTypeBadge = (type: string) => {
    const typeConfig = {
      COMPANY: { variant: 'primary', label: t('okrs:type.company', 'Công ty') },
      DEPARTMENT: { variant: 'secondary', label: t('okrs:type.department', 'Phòng ban') },
      INDIVIDUAL: { variant: 'default', label: t('okrs:type.individual', 'Cá nhân') },
    };

    const config = typeConfig[type as keyof typeof typeConfig] || typeConfig.INDIVIDUAL;
    return <Chip variant={config.variant as any}>{config.label}</Chip>;
  };

  // Render key result item
  const renderKeyResult = (keyResult: any) => (
    <div key={keyResult.id} className="p-3 rounded-lg bg-gray-50 dark:bg-gray-800">
      <div className="flex items-start justify-between mb-2">
        <Typography variant="body2" className="font-medium flex-1">
          {keyResult.title}
        </Typography>
        <div className="flex items-center gap-2">
          {renderStatusChip(keyResult.status)}
          <IconCard
            icon="edit"
            size="sm"
            variant="ghost"
            title={t('okrs:keyResult.edit', 'Cập nhật Key Result')}
            onClick={() => handleEditKeyResult(keyResult)}
            className="hover:bg-primary/10"
          />
        </div>
      </div>

      {keyResult.description && (
        <Typography variant="caption" className="text-gray-600 dark:text-gray-400 mb-2">
          {keyResult.description || ''}
        </Typography>
      )}

      <div className="flex items-center justify-between mb-2">
        <Typography variant="caption" className="text-gray-500">
          {keyResult.currentValue || 0} / {keyResult.targetValue} {keyResult.unit || ''}
        </Typography>
        <Typography variant="caption" className="font-medium">
          {keyResult.progress || 0}%
        </Typography>
      </div>

      <ProgressBar
        value={keyResult.progress || 0}
        size="sm"
        color={
          keyResult.progress >= 100 ? 'success' : keyResult.progress >= 70 ? 'primary' : 'warning'
        }
      />
    </div>
  );

  const cardTitle = (
    <div className="flex items-start justify-between w-full">
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <Typography variant="h6" className="font-semibold truncate">
            {objective.title}
          </Typography>
          {renderTypeBadge(objective.type)}
        </div>
        <div className="flex items-center gap-2">
          {renderStatusChip(objective.status)}
          <Typography variant="caption" className="text-gray-500">
            {keyResults?.length || 0} Key Results
          </Typography>
        </div>
      </div>
    </div>
  );

  return (
    <CollapsibleCard title={cardTitle} defaultOpen={false} className="h-fit" allowOverflow={true}>
      <div className="space-y-4">
        {/* Mô tả objective */}
        {objective.description && (
          <div>
            <Typography variant="body2" className="text-gray-700 dark:text-gray-300">
              {objective.description || ''}
            </Typography>
          </div>
        )}

        {/* Progress tổng thể */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <Typography variant="body2" className="font-medium">
              {t('okrs:objective.overallProgress', 'Tiến độ tổng thể')}
            </Typography>
            <Typography variant="body2" className="font-medium">
              {overallProgress}%
            </Typography>
          </div>
          <ProgressBar
            value={overallProgress}
            size="md"
            color={
              overallProgress >= 100 ? 'success' : overallProgress >= 70 ? 'primary' : 'warning'
            }
          />
        </div>

        {/* Danh sách Key Results */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <Typography variant="body2" className="font-medium">
              {t('okrs:keyResults', 'Key Results')}
            </Typography>
            {onAddKeyResult && (
              <Button variant="outline" size="sm" onClick={onAddKeyResult} className="text-xs">
                {t('okrs:keyResult.add', 'Thêm Key Result')}
              </Button>
            )}
          </div>

          {isLoadingKeyResults ? (
            <div className="flex items-center justify-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-2 border-primary border-t-transparent"></div>
            </div>
          ) : keyResults && keyResults.length > 0 ? (
            <div className="space-y-3">{keyResults.map(renderKeyResult)}</div>
          ) : (
            <div className="text-center py-4">
              <Typography variant="caption" className="text-gray-500">
                {t('okrs:keyResult.empty', 'Chưa có Key Result nào')}
              </Typography>
            </div>
          )}
        </div>

        {/* Thông tin thời gian */}
        <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>
              {t('okrs:objective.startDate', 'Bắt đầu')}: {objective.cycle?.startDate}
            </span>
            <span>
              {t('okrs:objective.endDate', 'Kết thúc')}: {objective.cycle?.endDate}
            </span>
          </div>
        </div>
      </div>

      {/* Modal cập nhật Key Result */}
      {isUpdateModalOpen && editingKeyResult && (
        <KeyResultUpdateModal
          keyResult={editingKeyResult}
          isOpen={isUpdateModalOpen}
          onClose={handleCloseUpdateModal}
          onUpdate={handleUpdateKeyResult}
        />
      )}
    </CollapsibleCard>
  );
};

export default ObjectiveCard;
